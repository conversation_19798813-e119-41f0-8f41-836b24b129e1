/**
 * @file Governance Types
 * @filepath shared/src/types/platform/governance/governance-types.ts
 * @task-id M-TSK-01.SUB-04.1.TYP-04
 * @component governance-types
 * @reference foundation-context.TYPES.004
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Type-Definitions
 * @created 2025-07-28 16:00:00 +03
 * @modified 2025-07-28 16:00:00 +03
 *
 * @description
 * Governance types module providing:
 * - Type definitions for governance system components
 * - Rule execution metrics and status tracking types
 * - Governance workflow and process type definitions
 * - Enterprise-grade governance type structures
 * - Performance-optimized type definitions for governance operations
 * - Integration type definitions for governance system coordination
 * - Type safety for governance rule execution and monitoring
 * - Comprehensive type coverage for governance functionality
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-012-governance-types-architecture
 * @governance-dcr DCR-foundation-012-governance-types-development
 * @governance-status approved
 * @governance-compliance authority-validated
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/types/platform/tracking/tracking-types
 * @enables server/src/platform/governance/core-managers/GovernanceManager
 * @enables server/src/platform/governance/automation-processing/GovernanceProcessor
 * @related-contexts foundation-context, governance-context, type-definitions-context
 * @governance-impact framework-foundation, governance-system, type-safety
 *
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type type-definitions
 * @lifecycle-stage implementation
 * @testing-status type-checked
 * @deployment-ready true
 * @monitoring-enabled false
 * @documentation docs/contexts/governance-context/types/governance-types.md
 *
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   anti-simplification-compliant: true
 *
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-07-28) - Initial governance types implementation
 * v1.1.0 (2025-07-28) - Added comprehensive type structures for governance system
 */

import { TTimeRange } from '../tracking/tracking-types';

/**
 * Rule execution metrics type
 */
export type TRuleExecutionMetrics = {
  ruleId: string;
  executionTime: number;
  status: 'success' | 'failure' | 'timeout';
  resourceUsage: {
    cpu: number;
    memory: number;
    network: number;
  };
  timestamp: Date;
  metadata: Record<string, any>;
};

/**
 * Compliance metrics type
 */
export type TComplianceMetrics = {
  ruleId: string;
  complianceScore: number;
  violations: number;
  timestamp: Date;
  metadata: Record<string, any>;
};

/**
 * Rule performance metrics type
 */
export type TRulePerformanceMetrics = {
  ruleId: string;
  timeRange: TTimeRange;
  executionMetrics: {
    averageExecutionTime: number;
    totalExecutions: number;
    executionTimeData: any[];
  };
  resourceMetrics: {
    averageMemoryUsage: number;
    averageCpuUsage: number;
    memoryUsageData: any[];
    cpuUsageData: any[];
  };
  performanceScore: number;
  recommendations: string[];
};

/**
 * System metrics type
 */
export type TSystemMetrics = {
  timeRange: TTimeRange;
  metrics: {
    ruleExecutions: any;
    ruleResults: any;
    complianceScores: any;
    complianceViolations: any;
    memoryUsage: any;
    cpuUsage: any;
    systemHealth: {
      totalDataPointsCollected: number;
      totalAlertsGenerated: number;
      avgCollectionTimeMs: number;
      errorCount: number;
      activeAlerts: number;
    };
  };
  systemScore: number;
  recommendations: string[];
  summary: {
    totalRuleExecutions: number;
    averageExecutionTime: number;
    systemHealthScore: number;
    activeAlertsCount: number;
  };
};

/**
 * Metrics dashboard type
 */
export type TMetricsDashboard = {
  dashboardId: string;
  title: string;
  description: string;
  timeRange: TTimeRange;
  panels: Array<{
    panelId: string;
    title: string;
    type: 'chart' | 'gauge' | 'table' | 'stat';
    metrics: any[];
    configuration: Record<string, any>;
  }>;
  metadata: Record<string, any>;
};

/**
 * Export format type
 */
export type TExportFormat = 'json' | 'csv' | 'excel' | 'pdf';

/**
 * Export result type
 */
export type TExportResult = {
  exportId: string;
  format: TExportFormat;
  url: string;
  expiresAt: Date;
  metadata: Record<string, any>;
};

// ============================================================================
// INTEGRATION SERVICE TYPES
// ============================================================================

/**
 * Integration service data type
 */
export type TIntegrationService = {
  serviceId: string;
  serviceName: string;
  serviceVersion: string;
  serviceType: 'bridge' | 'coordinator' | 'validator' | 'monitor';
  serviceStatus: 'active' | 'inactive' | 'degraded' | 'maintenance';
  bridgeConnections: TBridgeConnection[];
  integrationMetrics: TIntegrationMetrics;
  lastHealthCheck: Date;
  serviceMetadata: Record<string, unknown>;
};

/**
 * Bridge connection type
 */
export type TBridgeConnection = {
  connectionId: string;
  sourceSystem: string;
  targetSystem: string;
  connectionType: 'bidirectional' | 'unidirectional';
  status: 'connected' | 'disconnected' | 'error';
  latency: number;
  throughput: number;
  errorRate: number;
  lastSync: Date;
  metadata: Record<string, unknown>;
};

/**
 * Integration metrics type
 */
export type TIntegrationMetrics = {
  totalOperations: number;
  successfulOperations: number;
  failedOperations: number;
  averageLatency: number;
  throughput: number;
  errorRate: number;
  uptime: number;
  lastUpdate: Date;
  performanceMetrics: Record<string, number>;
};

/**
 * Governance-Tracking Bridge data type
 */
export type TGovernanceTrackingBridgeData = TIntegrationService & {
  governanceSystemConfig: TGovernanceSystemConfig;
  trackingSystemConfig: TTrackingSystemConfig;
  synchronizationStatus: TSynchronizationStatus;
  eventHandlers: TEventHandler[];
  complianceValidators: TComplianceValidator[];
  bridgeHealth: TBridgeHealthStatus;
  diagnosticsHistory: TDiagnosticsRecord[];
};

/**
 * Governance system configuration type
 */
export type TGovernanceSystemConfig = {
  systemId: string;
  systemName: string;
  version: string;
  endpoints: TSystemEndpoint[];
  authentication: TAuthenticationConfig;
  rulesSyncInterval: number;
  complianceCheckInterval: number;
  eventSubscriptions: string[];
  metadata: Record<string, unknown>;
};

/**
 * Tracking system configuration type
 */
export type TTrackingSystemConfig = {
  systemId: string;
  systemName: string;
  version: string;
  endpoints: TSystemEndpoint[];
  authentication: TAuthenticationConfig;
  dataSyncInterval: number;
  metricsCollectionInterval: number;
  eventSubscriptions: string[];
  metadata: Record<string, unknown>;
};

/**
 * System endpoint type
 */
export type TSystemEndpoint = {
  endpointId: string;
  name: string;
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  authentication: boolean;
  timeout: number;
  retryPolicy: TRetryPolicy;
  metadata: Record<string, unknown>;
};

/**
 * Authentication configuration type
 */
export type TAuthenticationConfig = {
  type: 'bearer' | 'basic' | 'oauth' | 'apikey';
  credentials: Record<string, string>;
  tokenRefreshInterval?: number;
  metadata: Record<string, unknown>;
};

/**
 * Retry policy type
 */
export type TRetryPolicy = {
  maxAttempts: number;
  initialDelay: number;
  backoffMultiplier: number;
  maxDelay: number;
  retryableErrors: string[];
};

// ============================================================================
// BRIDGE OPERATION TYPES
// ============================================================================

/**
 * Bridge configuration type
 */
export type TBridgeConfig = {
  bridgeId: string;
  bridgeName: string;
  governanceSystem: TGovernanceSystemConfig;
  trackingSystem: TTrackingSystemConfig;
  synchronizationSettings: TSynchronizationSettings;
  eventHandlingSettings: TEventHandlingSettings;
  healthCheckSettings: THealthCheckSettings;
  diagnosticsSettings: TDiagnosticsSettings;
  metadata: Record<string, unknown>;
};

/**
 * Synchronization settings type
 */
export type TSynchronizationSettings = {
  enabled: boolean;
  interval: number;
  batchSize: number;
  retryPolicy: TRetryPolicy;
  conflictResolution: 'governance-wins' | 'tracking-wins' | 'manual';
  metadata: Record<string, unknown>;
};

/**
 * Event handling settings type
 */
export type TEventHandlingSettings = {
  enabled: boolean;
  eventTypes: string[];
  processingMode: 'sync' | 'async';
  bufferSize: number;
  timeout: number;
  retryPolicy: TRetryPolicy;
  metadata: Record<string, unknown>;
};

/**
 * Health check settings type
 */
export type THealthCheckSettings = {
  enabled: boolean;
  interval: number;
  timeout: number;
  thresholds: THealthThresholds;
  alerting: TAlertingConfig;
  metadata: Record<string, unknown>;
};

/**
 * Diagnostics settings type
 */
export type TDiagnosticsSettings = {
  enabled: boolean;
  level: 'basic' | 'detailed' | 'comprehensive';
  retentionPeriod: number;
  exportEnabled: boolean;
  metadata: Record<string, unknown>;
};

/**
 * Health thresholds type
 */
export type THealthThresholds = {
  latency: number;
  errorRate: number;
  throughput: number;
  uptime: number;
  memoryUsage: number;
  cpuUsage: number;
};

/**
 * Alerting configuration type
 */
export type TAlertingConfig = {
  enabled: boolean;
  channels: string[];
  severity: 'low' | 'medium' | 'high' | 'critical';
  escalation: TEscalationPolicy;
  metadata: Record<string, unknown>;
};

/**
 * Escalation policy type
 */
export type TEscalationPolicy = {
  enabled: boolean;
  levels: TEscalationLevel[];
  timeout: number;
  metadata: Record<string, unknown>;
};

/**
 * Escalation level type
 */
export type TEscalationLevel = {
  level: number;
  delay: number;
  channels: string[];
  recipients: string[];
  metadata: Record<string, unknown>;
};

// ============================================================================
// BRIDGE RESULT TYPES
// ============================================================================

/**
 * Bridge initialization result type
 */
export type TBridgeInitResult = {
  success: boolean;
  bridgeId: string;
  timestamp: Date;
  governanceConnection: TConnectionStatus;
  trackingConnection: TConnectionStatus;
  errors: TBridgeError[];
  warnings: string[];
  metadata: Record<string, unknown>;
};

/**
 * Connection status type
 */
export type TConnectionStatus = {
  connected: boolean;
  latency: number;
  lastCheck: Date;
  errorCount: number;
  metadata: Record<string, unknown>;
};

/**
 * Bridge error type
 */
export type TBridgeError = {
  errorId: string;
  type: 'connection' | 'authentication' | 'timeout' | 'validation' | 'system';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: Date;
  context: Record<string, unknown>;
  stackTrace?: string;
};

/**
 * Synchronization result type
 */
export type TSynchronizationResult = {
  success: boolean;
  syncId: string;
  timestamp: Date;
  rulesProcessed: number;
  rulesSuccessful: number;
  rulesFailed: number;
  conflicts: TSyncConflict[];
  errors: TBridgeError[];
  duration: number;
  metadata: Record<string, unknown>;
};

/**
 * Synchronization conflict type
 */
export type TSyncConflict = {
  conflictId: string;
  ruleId: string;
  type: 'version' | 'content' | 'authority' | 'dependency';
  governanceValue: unknown;
  trackingValue: unknown;
  resolution: 'governance-wins' | 'tracking-wins' | 'manual' | 'pending';
  timestamp: Date;
  metadata: Record<string, unknown>;
};

/**
 * Forwarding result type
 */
export type TForwardingResult = {
  success: boolean;
  forwardingId: string;
  timestamp: Date;
  dataSize: number;
  processingTime: number;
  targetSystem: string;
  errors: TBridgeError[];
  metadata: Record<string, unknown>;
};

/**
 * Compliance validation result type
 */
export type TComplianceValidationResult = {
  success: boolean;
  validationId: string;
  timestamp: Date;
  scope: TValidationScope;
  complianceScore: number;
  violations: TComplianceViolation[];
  recommendations: string[];
  errors: TBridgeError[];
  metadata: Record<string, unknown>;
};

/**
 * Validation scope type
 */
export type TValidationScope = {
  systems: string[];
  ruleTypes: string[];
  timeRange: TTimeRange;
  includeHistorical: boolean;
  metadata: Record<string, unknown>;
};

/**
 * Compliance violation type
 */
export type TComplianceViolation = {
  violationId: string;
  type: 'rule' | 'process' | 'data' | 'authority' | 'security';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  affectedSystems: string[];
  remediation: string;
  timestamp: Date;
  metadata: Record<string, unknown>;
};

// ============================================================================
// BRIDGE STATUS AND MONITORING TYPES
// ============================================================================

/**
 * Bridge health status type
 */
export type TBridgeHealthStatus = {
  overall: 'healthy' | 'degraded' | 'unhealthy' | 'critical';
  governanceSystem: TSystemHealth;
  trackingSystem: TSystemHealth;
  bridgeComponents: TComponentHealth[];
  lastCheck: Date;
  uptime: number;
  metrics: TBridgeMetrics;
  alerts: THealthAlert[];
  metadata: Record<string, unknown>;
};

/**
 * System health type
 */
export type TSystemHealth = {
  status: 'healthy' | 'degraded' | 'unhealthy' | 'critical';
  latency: number;
  errorRate: number;
  throughput: number;
  lastResponse: Date;
  errors: TBridgeError[];
  metadata: Record<string, unknown>;
};

/**
 * Component health type
 */
export type TComponentHealth = {
  componentName: string;
  status: 'healthy' | 'degraded' | 'unhealthy' | 'critical';
  lastCheck: Date;
  metrics: Record<string, number>;
  errors: TBridgeError[];
  metadata: Record<string, unknown>;
};

/**
 * Bridge metrics type
 */
export type TBridgeMetrics = {
  operationsPerSecond: number;
  averageLatency: number;
  errorRate: number;
  throughput: number;
  uptime: number;
  memoryUsage: number;
  cpuUsage: number;
  networkUsage: number;
  cacheHitRate: number;
  queueSize: number;
  lastUpdate: Date;
  historicalData: TMetricsDataPoint[];
  metadata: Record<string, unknown>;
};

/**
 * Metrics data point type
 */
export type TMetricsDataPoint = {
  timestamp: Date;
  value: number;
  metric: string;
  metadata: Record<string, unknown>;
};

/**
 * Health alert type
 */
export type THealthAlert = {
  alertId: string;
  type: 'performance' | 'error' | 'availability' | 'security';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: Date;
  acknowledged: boolean;
  resolvedAt?: Date;
  metadata: Record<string, unknown>;
};

/**
 * Diagnostics result type
 */
export type TDiagnosticsResult = {
  diagnosticsId: string;
  timestamp: Date;
  level: 'basic' | 'detailed' | 'comprehensive';
  systemChecks: TSystemDiagnostic[];
  performanceAnalysis: TPerformanceAnalysis;
  recommendations: TDiagnosticRecommendation[];
  errors: TBridgeError[];
  duration: number;
  metadata: Record<string, unknown>;
};

/**
 * System diagnostic type
 */
export type TSystemDiagnostic = {
  systemName: string;
  checks: TDiagnosticCheck[];
  overall: 'pass' | 'warning' | 'fail';
  metadata: Record<string, unknown>;
};

/**
 * Diagnostic check type
 */
export type TDiagnosticCheck = {
  checkName: string;
  status: 'pass' | 'warning' | 'fail';
  message: string;
  value?: number;
  threshold?: number;
  metadata: Record<string, unknown>;
};

/**
 * Performance analysis type
 */
export type TPerformanceAnalysis = {
  latencyAnalysis: TLatencyAnalysis;
  throughputAnalysis: TThroughputAnalysis;
  errorAnalysis: TErrorAnalysis;
  resourceAnalysis: TResourceAnalysis;
  recommendations: string[];
  metadata: Record<string, unknown>;
};

/**
 * Latency analysis type
 */
export type TLatencyAnalysis = {
  average: number;
  median: number;
  p95: number;
  p99: number;
  trend: 'improving' | 'stable' | 'degrading';
  metadata: Record<string, unknown>;
};

/**
 * Throughput analysis type
 */
export type TThroughputAnalysis = {
  current: number;
  average: number;
  peak: number;
  trend: 'improving' | 'stable' | 'degrading';
  metadata: Record<string, unknown>;
};

/**
 * Error analysis type
 */
export type TErrorAnalysis = {
  errorRate: number;
  errorTypes: Record<string, number>;
  trend: 'improving' | 'stable' | 'degrading';
  topErrors: TBridgeError[];
  metadata: Record<string, unknown>;
};

/**
 * Resource analysis type
 */
export type TResourceAnalysis = {
  memoryUsage: number;
  cpuUsage: number;
  networkUsage: number;
  diskUsage: number;
  trends: Record<string, 'improving' | 'stable' | 'degrading'>;
  metadata: Record<string, unknown>;
};

/**
 * Diagnostic recommendation type
 */
export type TDiagnosticRecommendation = {
  type: 'performance' | 'reliability' | 'security' | 'maintenance';
  priority: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  action: string;
  impact: string;
  metadata: Record<string, unknown>;
};

// ============================================================================
// EVENT HANDLING TYPES
// ============================================================================

/**
 * Event handler type
 */
export type TEventHandler = {
  handlerId: string;
  eventType: string;
  sourceSystem: string;
  targetSystem: string;
  processingMode: 'sync' | 'async';
  enabled: boolean;
  priority: number;
  retryPolicy: TRetryPolicy;
  metadata: Record<string, unknown>;
};

/**
 * Governance event type
 */
export type TGovernanceEvent = {
  eventId: string;
  eventType: string;
  source: string;
  timestamp: Date;
  data: Record<string, unknown>;
  metadata: Record<string, unknown>;
};

/**
 * Tracking event type
 */
export type TTrackingEvent = {
  eventId: string;
  eventType: string;
  source: string;
  timestamp: Date;
  data: Record<string, unknown>;
  metadata: Record<string, unknown>;
};

/**
 * Event handling result type
 */
export type TEventHandlingResult = {
  success: boolean;
  eventId: string;
  handlerId: string;
  timestamp: Date;
  processingTime: number;
  errors: TBridgeError[];
  metadata: Record<string, unknown>;
};

/**
 * Reset result type
 */
export type TResetResult = {
  success: boolean;
  resetId: string;
  timestamp: Date;
  componentsReset: string[];
  errors: TBridgeError[];
  duration: number;
  metadata: Record<string, unknown>;
};

// ============================================================================
// SUPPORTING TYPES
// ============================================================================

/**
 * Synchronization status type
 */
export type TSynchronizationStatus = {
  enabled: boolean;
  lastSync: Date;
  nextSync: Date;
  syncInProgress: boolean;
  totalSyncs: number;
  successfulSyncs: number;
  failedSyncs: number;
  averageSyncTime: number;
  lastSyncResult: TSynchronizationResult | null;
  metadata: Record<string, unknown>;
};

/**
 * Compliance validator type
 */
export type TComplianceValidator = {
  validatorId: string;
  name: string;
  type: string;
  enabled: boolean;
  rules: string[];
  priority: number;
  metadata: Record<string, unknown>;
};

/**
 * Diagnostics record type
 */
export type TDiagnosticsRecord = {
  recordId: string;
  timestamp: Date;
  level: 'basic' | 'detailed' | 'comprehensive';
  result: TDiagnosticsResult;
  metadata: Record<string, unknown>;
};

// ============================================================================
// INTEGRATION DATA TYPES (from existing framework)
// ============================================================================

/**
 * Integration data type
 */
export type TIntegrationData = {
  dataId: string;
  sourceSystem: string;
  targetSystem: string;
  dataType: string;
  payload: Record<string, unknown>;
  timestamp: Date;
  metadata: Record<string, unknown>;
};

/**
 * Processing result type
 */
export type TProcessingResult = {
  success: boolean;
  processingId: string;
  timestamp: Date;
  processingTime: number;
  errors: TBridgeError[];
  metadata: Record<string, unknown>;
};

/**
 * Monitoring status type
 */
export type TMonitoringStatus = {
  status: 'active' | 'inactive' | 'degraded' | 'error';
  lastCheck: Date;
  metrics: Record<string, number>;
  alerts: THealthAlert[];
  metadata: Record<string, unknown>;
};

/**
 * Optimization result type
 */
export type TOptimizationResult = {
  success: boolean;
  optimizationId: string;
  timestamp: Date;
  improvements: TOptimizationImprovement[];
  performanceGain: number;
  errors: TBridgeError[];
  metadata: Record<string, unknown>;
};

/**
 * Optimization improvement type
 */
export type TOptimizationImprovement = {
  area: string;
  description: string;
  impact: 'low' | 'medium' | 'high';
  beforeValue: number;
  afterValue: number;
  metadata: Record<string, unknown>;
};